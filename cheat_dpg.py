import pymem
import pymem.process
import time
import dearpygui.dearpygui as dpg
import threading
import sys

# --- 配置区 ---
PROCESS_NAME = "TheSpellBrigade.exe"
# ----------------------------------------

def find_pointer_address(pm, base, offsets):
    """
    根据基址和多级偏移计算最终地址
    """
    try:
        # 在64位游戏中，指针是8字节 (longlong)
        addr = pm.read_longlong(base)
        
        # 遍历偏移量列表，除了最后一个
        for offset in offsets[:-1]:
            # 如果地址为空，说明指针链断了
            if addr == 0:
                return None
            addr = pm.read_longlong(addr + offset)
            
        # 加上最后一个偏移量，得到最终地址
        if addr == 0:
            return None
        return addr + offsets[-1]
    except Exception:
        # 任何读取错误都意味着指针无效
        return None

class SpellBrigadeCheatDPG:
    def __init__(self):
        # 初始化变量
        self.pm = None
        self.is_connected = False
        self.monitoring = False
        self.current_gold = 0
        self.gold_address = None
        
        # 配置
        self.static_base_offset = 0x3858F00
        self.offsets = [0x20, 0xC0, 0x10, 0xB8, 0x0, 0x38]
        
        # 创建DPG上下文
        dpg.create_context()
        
        # 设置主题
        self.setup_theme()
        
        # 创建UI
        self.setup_ui()
        
    def setup_theme(self):
        """设置深色主题"""
        with dpg.theme() as global_theme:
            with dpg.theme_component(dpg.mvAll):
                dpg.add_theme_color(dpg.mvThemeCol_WindowBg, (40, 40, 40, 255))
                dpg.add_theme_color(dpg.mvThemeCol_ChildBg, (50, 50, 50, 255))
                dpg.add_theme_color(dpg.mvThemeCol_Button, (70, 130, 180, 255))
                dpg.add_theme_color(dpg.mvThemeCol_ButtonHovered, (100, 149, 237, 255))
                dpg.add_theme_color(dpg.mvThemeCol_ButtonActive, (65, 105, 225, 255))
                dpg.add_theme_color(dpg.mvThemeCol_FrameBg, (60, 60, 60, 255))
                dpg.add_theme_color(dpg.mvThemeCol_Text, (255, 255, 255, 255))
                dpg.add_theme_color(dpg.mvThemeCol_TitleBg, (30, 30, 30, 255))
                dpg.add_theme_color(dpg.mvThemeCol_TitleBgActive, (50, 50, 50, 255))
                
        dpg.bind_theme(global_theme)
        
    def setup_ui(self):
        """创建用户界面"""
        with dpg.window(label="The Spell Brigade 金币修改器", tag="main_window"):
            # 标题
            dpg.add_text("The Spell Brigade 金币修改器", color=(255, 215, 0))
            dpg.add_separator()
            
            # 连接状态
            with dpg.group(horizontal=True):
                dpg.add_text("游戏连接状态:")
                dpg.add_text("未连接", tag="status_text", color=(255, 68, 68))
            
            dpg.add_spacing(count=2)
            
            # 连接按钮
            dpg.add_button(label="连接游戏", tag="connect_btn", callback=self.connect_game,
                          width=120, height=30)
            
            dpg.add_spacing(count=3)
            
            # 当前金币显示
            with dpg.group(horizontal=True):
                dpg.add_text("当前金币:")
                dpg.add_text("0", tag="gold_display", color=(255, 221, 68))
            
            dpg.add_spacing(count=3)
            
            # 修改金币区域
            dpg.add_text("修改金币数量:")
            with dpg.group(horizontal=True):
                dpg.add_input_int(tag="gold_input", width=150, default_value=10000)
                dpg.add_button(label="修改金币", tag="modify_btn", callback=self.modify_gold,
                              enabled=False, width=100)
            
            dpg.add_spacing(count=2)
            
            # 快捷金币按钮
            dpg.add_text("快捷设置:")
            with dpg.group(horizontal=True):
                quick_amounts = [1000, 5000, 10000, 50000, 100000, 999999]
                for amount in quick_amounts:
                    dpg.add_button(label=f"{amount:,}", 
                                  callback=lambda s, a, u, amount=amount: self.set_quick_amount(amount),
                                  width=80, enabled=False, tag=f"quick_{amount}")
            
            dpg.add_spacing(count=3)
            
            # 日志区域
            dpg.add_text("操作日志:")
            dpg.add_child_window(tag="log_window", height=200, border=True)
            
        # 设置主窗口
        dpg.set_primary_window("main_window", True)
        
        # 初始日志
        self.log("程序启动完成")
        self.log("提示：请先启动游戏，然后点击'连接游戏'按钮")
        
    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        
        # 在日志窗口中添加文本
        with dpg.window(parent="log_window", autosize=True, no_title_bar=True, 
                       no_resize=True, no_move=True, no_scrollbar=True):
            dpg.add_text(log_message, wrap=400)
            
        # 滚动到底部
        if dpg.does_item_exist("log_window"):
            dpg.set_y_scroll("log_window", -1.0)
        
    def connect_game(self):
        """连接到游戏进程"""
        if self.is_connected:
            self.disconnect_game()
            return
            
        try:
            self.log(f"正在查找游戏进程: {PROCESS_NAME}...")
            self.pm = pymem.Pymem(PROCESS_NAME)
            self.log(f"成功附加到进程！进程ID: {self.pm.process_id}")
            
            # 获取模块基址
            module_base = pymem.process.module_from_name(self.pm.process_handle, "GameAssembly.dll").lpBaseOfDll
            self.log(f"GameAssembly.dll 模块基址: {hex(module_base)}")
            self.module_base = module_base
            
            self.is_connected = True
            dpg.set_value("status_text", "已连接")
            dpg.configure_item("status_text", color=(68, 255, 68))
            dpg.configure_item("connect_btn", label="断开连接")
            dpg.configure_item("modify_btn", enabled=True)
            
            # 启用快捷按钮
            quick_amounts = [1000, 5000, 10000, 50000, 100000, 999999]
            for amount in quick_amounts:
                dpg.configure_item(f"quick_{amount}", enabled=True)
            
            # 开始监控金币
            self.start_monitoring()
            
        except pymem.exception.ProcessNotFound:
            self.log(f"错误: 未找到游戏进程 '{PROCESS_NAME}'")
            self.log("请先启动游戏，然后再尝试连接")
        except Exception as e:
            self.log(f"连接失败: {e}")
            
    def disconnect_game(self):
        """断开游戏连接"""
        self.monitoring = False
        self.is_connected = False
        self.pm = None
        
        dpg.set_value("status_text", "未连接")
        dpg.configure_item("status_text", color=(255, 68, 68))
        dpg.configure_item("connect_btn", label="连接游戏")
        dpg.configure_item("modify_btn", enabled=False)
        dpg.set_value("gold_display", "0")
        
        # 禁用快捷按钮
        quick_amounts = [1000, 5000, 10000, 50000, 100000, 999999]
        for amount in quick_amounts:
            dpg.configure_item(f"quick_{amount}", enabled=False)
            
        self.log("已断开游戏连接")
        
    def start_monitoring(self):
        """开始监控金币"""
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_gold, daemon=True)
        self.monitor_thread.start()
        
    def monitor_gold(self):
        """监控金币数量"""
        while self.monitoring and self.is_connected:
            try:
                gold_address = find_pointer_address(self.pm, self.module_base + self.static_base_offset, self.offsets)
                
                if gold_address:
                    current_gold = self.pm.read_int(gold_address)
                    self.current_gold = current_gold
                    self.gold_address = gold_address
                    
                    # 更新UI
                    dpg.set_value("gold_display", f"{current_gold:,}")
                else:
                    dpg.set_value("gold_display", "未检测到")
                    
            except Exception as e:
                self.log(f"监控错误: {e}")
                break
                
            time.sleep(0.5)  # 每0.5秒检查一次
            
    def set_quick_amount(self, amount):
        """设置快捷金币数量"""
        dpg.set_value("gold_input", amount)
        
    def modify_gold(self):
        """修改金币"""
        if not self.is_connected or not self.gold_address:
            self.log("错误: 请先连接游戏并等待检测到金币地址")
            return
            
        try:
            new_gold = dpg.get_value("gold_input")
            if new_gold < 0:
                self.log("错误: 金币数量不能为负数")
                return
                
            # 修改金币
            self.pm.write_int(self.gold_address, new_gold)
            
            # 验证修改
            time.sleep(0.1)
            actual_gold = self.pm.read_int(self.gold_address)
            
            if actual_gold == new_gold:
                self.log(f"成功！金币已修改为: {new_gold:,}")
            else:
                self.log("写入失败！可能被游戏保护机制阻止")
                
        except Exception as e:
            self.log(f"修改金币时发生错误: {e}")
            
    def run(self):
        """运行应用程序"""
        dpg.create_viewport(title="The Spell Brigade 金币修改器", width=600, height=500)
        dpg.setup_dearpygui()
        dpg.show_viewport()
        
        # 主循环
        while dpg.is_dearpygui_running():
            dpg.render_dearpygui_frame()
            
        # 清理
        if self.is_connected:
            self.disconnect_game()
        dpg.destroy_context()

def main():
    """主程序入口"""
    app = SpellBrigadeCheatDPG()
    app.run()

if __name__ == "__main__":
    main()
