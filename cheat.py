import pymem
import pymem.process
import time

# --- 配置区 (已根据你的信息填写完毕) ---
PROCESS_NAME = "TheSpellBrigade.exe"
# ----------------------------------------

def find_pointer_address(pm, base, offsets):
    """
    根据基址和多级偏移计算最终地址
    """
    try:
        # 在64位游戏中，指针是8字节 (longlong)
        addr = pm.read_longlong(base)
        
        # 遍历偏移量列表，除了最后一个
        for offset in offsets[:-1]:
            # 如果地址为空，说明指针链断了
            if addr == 0:
                return None
            addr = pm.read_longlong(addr + offset)
            
        # 加上最后一个偏移量，得到最终地址
        if addr == 0:
            return None
        return addr + offsets[-1]
    except Exception:
        # 任何读取错误都意味着指针无效
        return None

def modify_gold():
    """主修改逻辑"""
    print("--- 《The Spell Brigade》金币修改器 ---")
    print("提示：请先启动游戏，并以管理员身份运行此脚本！")

    try:
        # 1. 附加到游戏进程
        print(f"[*] 正在查找游戏进程: {PROCESS_NAME}...")
        pm = pymem.Pymem(PROCESS_NAME)
        print(f"[+] 成功附加到进程！进程ID: {pm.process_id}")
    except pymem.exception.ProcessNotFound:
        print(f"[-] 错误: 未找到游戏进程 '{PROCESS_NAME}'。")
        print("[-] 请先启动游戏，然后再运行此脚本。")
        return

    try:
        # 2. 获取 GameAssembly.dll 模块的基址
        module_base = pymem.process.module_from_name(pm.process_handle, "GameAssembly.dll").lpBaseOfDll
        print(f"[+] GameAssembly.dll 模块基址: {hex(module_base)}")

        # 3. 定义从你CE分析中得到的基址和偏移
        static_base_offset = 0x3858F00
        offsets = [0x20, 0xC0, 0x10, 0xB8, 0x0, 0x38]

        print("[*] 正在解析金币地址...")
        print(f"    基址: GameAssembly.dll + {hex(static_base_offset)}")
        print(f"    偏移: {[hex(o) for o in offsets]}")
        
        # 我们将持续监控和修改
        while True:
            # 4. 计算最终地址
            gold_address = find_pointer_address(pm, module_base + static_base_offset, offsets)

            if gold_address:
                try:
                    current_gold = pm.read_int(gold_address)
                    print(f"\r[*] 成功定位！当前金币: {current_gold} | 地址: {hex(gold_address)}  ", end="")
                    
                    # 这里可以加入修改逻辑，比如按键修改
                    # 为简单起见，我们只在第一次定位成功后询问一次
                    new_gold_str = input("\n[?] 请输入你想要修改的金币数量 (输入 'q' 退出): ")
                    if new_gold_str.lower() == 'q':
                        break
                    
                    new_gold = int(new_gold_str)
                    pm.write_int(gold_address, new_gold)

                    # 验证
                    time.sleep(0.1)
                    if pm.read_int(gold_address) == new_gold:
                        print(f"[!] 成功！金币已修改为: {new_gold}")
                        break # 修改成功后退出循环
                    else:
                        print("[-] 写入失败！")

                except (ValueError, TypeError):
                    print("\n[-] 无效输入，请输入一个纯数字。")
                except Exception as e:
                    print(f"\n[-] 读写内存时发生错误: {e}")
                    break
            else:
                print("\r[-] 正在等待玩家进入游戏... (无法解析指针)", end="")
                time.sleep(1) # 如果找不到地址，等1秒再试

    except Exception as e:
        print(f"\n[-] 发生未知错误: {e}")

if __name__ == "__main__":
    modify_gold()
    input("\n修改完成，按回车键退出...")

